<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Firebase CRUD Web App</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- PDF Export Library -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
  <!-- Excel Export Library -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
  <style>
    * { box-sizing: border-box; margin: 0; padding: 0; }
    body { 
      font-family: Inter, Arial, sans-serif; 
      background: linear-gradient(180deg, #ffb300, #ff9500); 
      min-height: 100vh;
      padding: 24px;
    }
    .card { 
      background: #fff; 
      border-radius: 12px; 
      padding: 20px; 
      box-shadow: 0 8px 24px rgba(0,0,0,0.12); 
      margin-bottom: 20px; 
    }
    .table-container { 
      overflow-x: auto; 
      -webkit-overflow-scrolling: touch; 
    }
    table { 
      width: 100%; 
      border-collapse: collapse; 
      min-width: 800px; 
    }
    th, td { 
      padding: 12px 10px; 
      border-bottom: 1px solid #eee; 
      text-align: left; 
      font-size: 14px; 
    }
    th { 
      background: #fff8e1; 
      font-weight: 600; 
      position: sticky; 
      top: 0; 
      z-index: 10; 
    }
    .btn { 
      padding: 8px 16px; 
      border-radius: 6px; 
      font-size: 14px; 
      cursor: pointer; 
      font-weight: 500; 
      border: none; 
      transition: all 0.2s; 
      white-space: nowrap; 
    }
    .btn:active { transform: scale(0.98); }
    .btn-add { background: #f59e0b; color: #fff; }
    .btn-add:hover { background: #d97706; }
    .btn-print { background: #3b82f6; color: #fff; }
    .btn-print:hover { background: #2563eb; }
    .btn-pdf { background: #dc2626; color: #fff; }
    .btn-pdf:hover { background: #b91c1c; }
    .btn-excel { background: #16a34a; color: #fff; }
    .btn-excel:hover { background: #15803d; }
    .export-dropdown {
      position: relative;
      display: inline-block;
    }
    .dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      background-color: white;
      min-width: 180px;
      box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
      border-radius: 8px;
      z-index: 1;
      border: 1px solid #e5e7eb;
    }
    .dropdown-content.show {
      display: block;
      animation: fadeIn 0.2s;
    }
    .dropdown-content button {
      color: #374151;
      padding: 12px 16px;
      text-decoration: none;
      display: block;
      width: 100%;
      text-align: left;
      border: none;
      background: none;
      cursor: pointer;
      font-size: 14px;
      border-radius: 0;
    }
    .dropdown-content button:hover {
      background-color: #f3f4f6;
    }
    .dropdown-content button:first-child {
      border-radius: 8px 8px 0 0;
    }
    .dropdown-content button:last-child {
      border-radius: 0 0 8px 8px;
    }
    .btn-edit { 
      background: #22c55e; 
      color: #fff; 
      padding: 6px 10px; 
      margin-right: 4px; 
    }
    .btn-edit:hover { background: #16a34a; }
    .btn-delete { 
      background: #ef4444; 
      color: #fff; 
      padding: 6px 10px; 
    }
    .btn-delete:hover { background: #dc2626; }
    .btn-cancel {
      background: #6b7280;
      color: #fff;
    }
    .btn-cancel:hover { background: #4b5563; }
    .status-active { 
      color: #16a34a; 
      font-weight: 600; 
      background: #dcfce7; 
      padding: 4px 8px; 
      border-radius: 4px; 
      display: inline-block; 
    }
    .overlay { 
      position: fixed; 
      inset: 0; 
      background: rgba(0,0,0,0.6); 
      display: none; 
      align-items: center; 
      justify-content: center; 
      z-index: 1000; 
      padding: 20px; 
    }
    .overlay.show { 
      display: flex; 
      animation: fadeIn 0.2s; 
    }
    .modal { 
      background: #fff; 
      padding: 24px; 
      border-radius: 12px; 
      width: 100%; 
      max-width: 450px; 
      max-height: 90vh; 
      overflow-y: auto; 
      animation: slideUp 0.3s; 
    }
    .modal h2 { 
      margin-bottom: 16px; 
      font-size: 18px;
      font-weight: 600;
    }
    .modal input { 
      width: 100%; 
      padding: 10px 12px; 
      margin: 8px 0; 
      border: 1px solid #ddd; 
      border-radius: 6px; 
      font-size: 14px; 
    }
    .modal input:focus { 
      outline: none; 
      border-color: #f59e0b; 
      box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1); 
    }
    .search-container { 
      position: relative; 
      max-width: 300px; 
    }
    .search-container input { 
      padding-left: 36px; 
      border: 1px solid #ddd; 
      border-radius: 6px; 
      padding: 8px 12px; 
      width: 100%;
    }
    .search-icon { 
      position: absolute; 
      left: 12px; 
      top: 50%; 
      transform: translateY(-50%); 
      color: #9ca3af; 
    }
    .empty-state { 
      text-align: center; 
      padding: 40px 20px; 
      color: #6b7280; 
    }
    .action-buttons { 
      display: flex; 
      gap: 4px; 
    }
    .header-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap;
      gap: 12px;
    }
    .header-buttons {
      display: flex;
      gap: 8px;
    }
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      flex-wrap: wrap;
      gap: 12px;
    }
    
    @keyframes fadeIn { 
      from { opacity: 0; } 
      to { opacity: 1; } 
    }
    @keyframes slideUp { 
      from { transform: translateY(20px); opacity: 0; } 
      to { transform: translateY(0); opacity: 1; } 
    }
    
    @media (max-width: 768px) {
      body { padding: 12px; }
      .card { padding: 16px; border-radius: 8px; }
      h1 { font-size: 16px; }
      .header-buttons { 
        flex-direction: column; 
        width: 100%; 
      }
      .btn { 
        width: 100%; 
        justify-content: center; 
        display: flex; 
        align-items: center; 
      }
      th, td { 
        padding: 8px 6px; 
        font-size: 12px; 
      }
      .modal { padding: 20px; }
      .search-container { max-width: 100%; }
    }
    
    @media print {
      body {
        background: white !important;
        padding: 20px !important;
        font-size: 12px;
      }
      .btn, .search-container, .overlay, .export-dropdown { display: none !important; }
      .card {
        box-shadow: none;
        border: 1px solid #ddd;
        page-break-inside: avoid;
      }
      .header-container {
        border-bottom: 2px solid #333;
        padding-bottom: 10px;
        margin-bottom: 20px;
      }
      .header-container h1 {
        color: #333 !important;
        font-size: 24px !important;
      }
      table {
        border-collapse: collapse;
        width: 100%;
        font-size: 11px;
      }
      th, td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }
      th {
        background-color: #f5f5f5 !important;
        font-weight: bold;
      }
      .status-active {
        background: none !important;
        color: #333 !important;
        border: 1px solid #333;
      }
      .print-header {
        display: block !important;
        text-align: center;
        margin-bottom: 20px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px;
      }
      .print-footer {
        display: block !important;
        text-align: center;
        margin-top: 20px;
        border-top: 1px solid #ddd;
        padding-top: 10px;
        font-size: 10px;
        color: #666;
      }
    }
    .print-header, .print-footer {
      display: none;
    }
  </style>
</head>
<body>
  <!-- Print Header (only visible when printing) -->
  <div class="print-header">
    <h1>🎓 Student Management System</h1>
    <p>Generated on: <span id="printDate"></span></p>
  </div>

  <!-- HEADER -->
  <div class="header-container">
    <h1 style="font-size: 20px; font-weight: bold; color: white;">🎓 Firebase CRUD Web App (Dynamic)</h1>
    <div class="header-buttons">
      <button class="btn btn-add" onclick="openForm()">+ Add Student</button>
      <div class="export-dropdown">
        <button class="btn btn-print" onclick="toggleExportDropdown()">📊 Export ▼</button>
        <div id="exportDropdown" class="dropdown-content">
          <button onclick="printTable()">🖨️ Print Table</button>
          <button onclick="exportToPDF()">📄 Export to PDF</button>
          <button onclick="exportToExcel()">📊 Export to Excel</button>
        </div>
      </div>
    </div>
  </div>

  <!-- CARD -->
  <div class="card">
    <div class="card-header">
      <h2 style="font-weight: 600; font-size: 18px;">All Students</h2>
      <div class="search-container">
        <input type="text" id="searchBox" placeholder="Search students...">
      </div>
    </div>
    <div class="table-container">
      <table id="studentsTable">
        <thead>
          <tr>
            <th>#</th>
            <th>Student Name</th>
            <th>Father Name</th>
            <th>Email ID</th>
            <th>Class</th>
            <th>Fees</th>
            <th>Status</th>
            <th>Created</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody id="studentRows">
          <!-- Dynamic rows will be inserted here -->
        </tbody>
      </table>
    </div>
    <div id="emptyState" class="empty-state" style="display: none;">
      <div style="font-size: 48px; margin-bottom: 12px;">📚</div>
      <p style="font-weight: 600; margin-bottom: 4px;">No students found</p>
      <p style="font-size: 14px;">Add your first student to get started</p>
    </div>
  </div>

  <!-- Print Footer (only visible when printing) -->
  <div class="print-footer">
    <p>Total Students: <span id="printTotal"></span> | Printed by: Firebase CRUD Web App</p>
  </div>

  <!-- MODAL FORM -->
  <div id="overlay" class="overlay">
    <div class="modal">
      <h2>Add / Update Student</h2>
      <form id="studentForm">
        <input type="text" id="studentName" placeholder="Student Name *" required>
        <input type="text" id="fatherName" placeholder="Father Name *" required>
        <input type="email" id="email" placeholder="Email ID *" required>
        <input type="text" id="className" placeholder="Class *" required>
        <input type="number" id="fees" placeholder="Fees *" step="0.01" required>
        <button type="submit" class="btn btn-add" style="margin-top: 12px; width: 100%;">💾 Save Student</button>
      </form>
      <button class="btn btn-cancel" style="margin-top: 8px; width: 100%;" onclick="closeForm()">Cancel</button>
    </div>
  </div>

  <script type="module">
    import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
    import { 
      getFirestore, 
      collection, 
      addDoc, 
      getDocs, 
      deleteDoc, 
      doc, 
      updateDoc, 
      serverTimestamp, 
      onSnapshot, 
      query, 
      orderBy 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    // 🔥 Firebase Configuration
    const firebaseConfig = {
      apiKey: "AIzaSyDjY8h3T65T7738SQ68vBgXLLelyQnueL4",
      authDomain: "numeric-dialect-420211.firebaseapp.com",
      projectId: "numeric-dialect-420211",
      storageBucket: "numeric-dialect-420211.firebasestorage.app",
      messagingSenderId: "269276551177",
      appId: "1:269276551177:web:cda776d48af62e9525c0d8",
      measurementId: "G-204JZJR3ML"
    };

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);

    // DOM Elements
    const studentForm = document.getElementById("studentForm");
    const studentRows = document.getElementById("studentRows");
    const overlay = document.getElementById("overlay");
    const searchBox = document.getElementById("searchBox");
    const emptyState = document.getElementById("emptyState");
    const studentsTable = document.getElementById("studentsTable");

    let editId = null;
    let allStudents = [];

    // 🟢 OPEN / CLOSE FORM
    window.openForm = () => { 
      overlay.classList.add('show');
      setTimeout(() => {
        document.getElementById("studentName").focus();
      }, 100);
    };

    window.closeForm = () => { 
      overlay.classList.remove('show');
      studentForm.reset(); 
      editId = null; 
    };

    // Close modal on overlay click
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) closeForm();
    });

    // � EXPORT FUNCTIONS

    // Toggle export dropdown
    window.toggleExportDropdown = () => {
      const dropdown = document.getElementById("exportDropdown");
      dropdown.classList.toggle("show");
    };

    // Close dropdown when clicking outside
    window.addEventListener('click', (e) => {
      if (!e.target.matches('.btn-print')) {
        const dropdown = document.getElementById("exportDropdown");
        if (dropdown.classList.contains('show')) {
          dropdown.classList.remove('show');
        }
      }
    });

    // Print function with better formatting
    window.printTable = () => {
      // Update print date and total
      document.getElementById('printDate').textContent = new Date().toLocaleDateString();
      document.getElementById('printTotal').textContent = allStudents.length;

      // Close dropdown
      document.getElementById("exportDropdown").classList.remove("show");

      // Print
      window.print();
    };

    // Export to PDF function
    window.exportToPDF = () => {
      try {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Add title
        doc.setFontSize(20);
        doc.setTextColor(40);
        doc.text('🎓 Student Management System', 20, 20);

        // Add date
        doc.setFontSize(12);
        doc.setTextColor(100);
        doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 30);
        doc.text(`Total Students: ${allStudents.length}`, 20, 40);

        // Prepare table data
        const tableData = allStudents.map((student, index) => [
          index + 1,
          student.name || 'N/A',
          student.father || 'N/A',
          student.email || 'N/A',
          student.class || 'N/A',
          `$${student.fees?.toFixed(2) || '0.00'}`,
          student.status || 'Active',
          student.createdAt?.toDate ? student.createdAt.toDate().toLocaleDateString() : 'N/A'
        ]);

        // Add table
        doc.autoTable({
          head: [['#', 'Student Name', 'Father Name', 'Email ID', 'Class', 'Fees', 'Status', 'Created']],
          body: tableData,
          startY: 50,
          styles: {
            fontSize: 10,
            cellPadding: 3,
          },
          headStyles: {
            fillColor: [245, 158, 11],
            textColor: 255,
            fontStyle: 'bold'
          },
          alternateRowStyles: {
            fillColor: [248, 250, 252]
          },
          margin: { top: 50 }
        });

        // Save the PDF
        doc.save(`students_${new Date().toISOString().split('T')[0]}.pdf`);

        // Close dropdown
        document.getElementById("exportDropdown").classList.remove("show");

        // Show success message
        alert('✅ PDF exported successfully!');

      } catch (error) {
        console.error('PDF Export Error:', error);
        alert('❌ Error exporting PDF. Please try again.');
      }
    };

    // Export to Excel function
    window.exportToExcel = () => {
      try {
        // Prepare data for Excel
        const excelData = allStudents.map((student, index) => ({
          '#': index + 1,
          'Student Name': student.name || 'N/A',
          'Father Name': student.father || 'N/A',
          'Email ID': student.email || 'N/A',
          'Class': student.class || 'N/A',
          'Fees': student.fees || 0,
          'Status': student.status || 'Active',
          'Created': student.createdAt?.toDate ? student.createdAt.toDate().toLocaleDateString() : 'N/A'
        }));

        // Create workbook and worksheet
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(excelData);

        // Set column widths
        const colWidths = [
          { wch: 5 },   // #
          { wch: 20 },  // Student Name
          { wch: 20 },  // Father Name
          { wch: 25 },  // Email ID
          { wch: 10 },  // Class
          { wch: 10 },  // Fees
          { wch: 10 },  // Status
          { wch: 12 }   // Created
        ];
        ws['!cols'] = colWidths;

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Students');

        // Save the Excel file
        XLSX.writeFile(wb, `students_${new Date().toISOString().split('T')[0]}.xlsx`);

        // Close dropdown
        document.getElementById("exportDropdown").classList.remove("show");

        // Show success message
        alert('✅ Excel file exported successfully!');

      } catch (error) {
        console.error('Excel Export Error:', error);
        alert('❌ Error exporting Excel file. Please try again.');
      }
    };

    // �🟢 CREATE / UPDATE STUDENT
    studentForm.addEventListener("submit", async (e) => {
      e.preventDefault();
      
      const student = {
        name: document.getElementById("studentName").value,
        father: document.getElementById("fatherName").value,
        email: document.getElementById("email").value,
        class: document.getElementById("className").value,
        fees: parseFloat(document.getElementById("fees").value),
        status: "Active",
        createdAt: serverTimestamp()
      };

      try {
        if (editId) {
          await updateDoc(doc(db, "students", editId), student);
          alert("✅ Student updated successfully!");
          editId = null;
        } else {
          await addDoc(collection(db, "students"), student);
          alert("✅ Student added successfully!");
        }
        closeForm();
      } catch (error) {
        console.error("Error saving student:", error);
        alert("❌ Error saving student. Please try again.");
      }
    });

    // 🔵 REAL-TIME LIST
    const q = query(collection(db, "students"), orderBy("createdAt", "desc"));
    onSnapshot(q, (snapshot) => {
      allStudents = [];
      snapshot.forEach(docSnap => {
        allStudents.push({ id: docSnap.id, ...docSnap.data() });
      });
      renderStudents(allStudents);
    });

    // Render students to table
    function renderStudents(students) {
      studentRows.innerHTML = "";
      
      if (students.length === 0) {
        studentsTable.style.display = "none";
        emptyState.style.display = "block";
        return;
      }
      
      studentsTable.style.display = "table";
      emptyState.style.display = "none";
      
      students.forEach((student, index) => {
        const tr = document.createElement("tr");
        const createdDate = student.createdAt?.toDate ? 
          student.createdAt.toDate().toLocaleDateString() : 'N/A';
        
        tr.innerHTML = `
          <td>${index + 1}</td>
          <td><strong>${student.name || 'N/A'}</strong></td>
          <td>${student.father || 'N/A'}</td>
          <td>${student.email || 'N/A'}</td>
          <td>${student.class || 'N/A'}</td>
          <td><strong>$${student.fees?.toFixed(2) || '0.00'}</strong></td>
          <td><span class="status-active">${student.status || 'Active'}</span></td>
          <td>${createdDate}</td>
          <td>
            <div class="action-buttons">
              <button class="btn btn-edit" onclick="editStudent('${student.id}')">✏️</button>
              <button class="btn btn-delete" onclick="deleteStudent('${student.id}')">🗑️</button>
            </div>
          </td>
        `;
        studentRows.appendChild(tr);
      });
    }

    // 🟣 EDIT STUDENT
    window.editStudent = (id) => {
      const student = allStudents.find(s => s.id === id);
      if (!student) return;
      
      editId = id;
      overlay.classList.add('show');
      document.getElementById("studentName").value = student.name || '';
      document.getElementById("fatherName").value = student.father || '';
      document.getElementById("email").value = student.email || '';
      document.getElementById("className").value = student.class || '';
      document.getElementById("fees").value = student.fees || '';
    };

    // 🔴 DELETE STUDENT
    window.deleteStudent = async (id) => {
      if (confirm("Are you sure you want to delete this student?")) {
        try {
          await deleteDoc(doc(db, "students", id));
          alert("✅ Student deleted successfully!");
        } catch (error) {
          console.error("Error deleting student:", error);
          alert("❌ Error deleting student. Please try again.");
        }
      }
    };

    // 🔍 SEARCH FILTER
    searchBox.addEventListener("input", (e) => {
      const filter = e.target.value.toLowerCase().trim();
      
      if (!filter) {
        renderStudents(allStudents);
        return;
      }
      
      const filtered = allStudents.filter(student => {
        return (
          student.name?.toLowerCase().includes(filter) ||
          student.father?.toLowerCase().includes(filter) ||
          student.email?.toLowerCase().includes(filter) ||
          student.class?.toLowerCase().includes(filter) ||
          student.fees?.toString().includes(filter)
        );
      });
      
      renderStudents(filtered);
    });
  </script>
</body>
</html>