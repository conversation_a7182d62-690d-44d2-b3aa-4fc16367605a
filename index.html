<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Firebase Contacts CRUD (Demo)</title>
  <style>
    body { font-family: Inter, system-ui, sans-serif; max-width:900px; margin:30px auto; padding:0 16px; background:#f6f7fb; color:#111; }
    h1 { margin-bottom:6px; }
    form { display:flex; gap:8px; flex-wrap:wrap; margin-bottom:14px; }
    input { padding:8px 10px; border-radius:8px; border:1px solid #d8dbe8; min-width:180px; }
    button { padding:8px 12px; border-radius:8px; border:0; cursor:pointer; background:#3b82f6; color:white; }
    ul { list-style:none; padding:0; margin:0; }
    li { background:white; padding:12px; border-radius:10px; margin:8px 0; display:flex; justify-content:space-between; align-items:center; gap:12px; border:1px solid #eceff6; }
    .meta { color:#6b7280; font-size:13px; }
    .controls button { margin-left:6px; background:#ef4444; }
    .controls button.edit { background:#f59e0b; }
    .small { font-size:13px; padding:6px 8px; border-radius:6px; }
  </style>
</head>
<body>

  <h1>Contacts (Firebase Firestore CRUD)</h1>
  <p style="color:#6b7280">Add, edit, and remove simple contacts. This uses Firestore in real-time.</p>

  <form id="contact-form">
    <input id="name" placeholder="Name" required />
    <input id="email" placeholder="Email (optional)" />
    <input id="phone" placeholder="Phone (optional)" />
    <button id="save-btn" class="small">Save Contact</button>
    <button id="cancel-edit" type="button" style="display:none" class="small">Cancel</button>
  </form>

  <ul id="contacts-list"></ul>

  <!-- Firebase compat SDK (simple for beginners). Replace version if you like. -->
  <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-firestore-compat.js"></script>

  <script>
  /********** 1) Paste your Firebase config here (get it from Project settings → General → Your apps) **********/
  const firebaseConfig = {
    apiKey: "AIzaSyDjY8h3T65T7738SQ68vBgXLLelyQnueL4",
    authDomain: "numeric-dialect-420211.firebaseapp.com",
    projectId: "numeric-dialect-420211",
    storageBucket: "numeric-dialect-420211.firebasestorage.app",
    messagingSenderId: "269276551177",
    appId: "1:269276551177:web:cda776d48af62e9525c0d8",
    measurementId: "G-204JZJR3ML"
  };

  // Initialize Firebase
  firebase.initializeApp(firebaseConfig);
  const db = firebase.firestore();


  // UI elements
  const form = document.getElementById('contact-form');
  const nameInput = document.getElementById('name');
  const emailInput = document.getElementById('email');
  const phoneInput = document.getElementById('phone');
  const saveBtn = document.getElementById('save-btn');
  const contactsList = document.getElementById('contacts-list');
  const cancelEditBtn = document.getElementById('cancel-edit');

  let editingId = null; // null => creating, otherwise editing

  // Save or update contact
  form.addEventListener('submit', (e) => {
    e.preventDefault();
    const name = nameInput.value.trim();
    const email = emailInput.value.trim();
    const phone = phoneInput.value.trim();
    if (!name) { alert('Please enter a name.'); return; }

    if (editingId) {
      // Update existing doc
      db.collection('contacts').doc(editingId).update({
        name, email, phone, updatedAt: firebase.firestore.FieldValue.serverTimestamp()
      }).then(() => {
        resetForm();
      }).catch(err => console.error('Update error', err));
    } else {
      // Add new doc
      db.collection('contacts').add({
        name, email, phone, createdAt: firebase.firestore.FieldValue.serverTimestamp()
      }).then(() => {
        resetForm();
      }).catch(err => console.error('Add error', err));
    }
  });

  cancelEditBtn.addEventListener('click', () => resetForm());

  function resetForm(){
    editingId = null;
    form.reset();
    saveBtn.textContent = 'Save Contact';
    cancelEditBtn.style.display = 'none';
  }

  // Real-time listener: automatically updates the list when Firestore changes
  db.collection('contacts').orderBy('createdAt', 'desc').onSnapshot(snapshot => {
    contactsList.innerHTML = '';
    if (snapshot.empty) {
      contactsList.innerHTML = '<li style="color:#6b7280">No contacts yet</li>';
      return;
    }
    snapshot.forEach(doc => {
      const data = doc.data();
      const li = document.createElement('li');
      li.innerHTML = `
        <div>
          <strong>${escapeHtml(data.name)}</strong>
          <div class="meta">${escapeHtml(data.email || '')} ${data.email && data.phone ? '•' : ''} ${escapeHtml(data.phone || '')}</div>
        </div>
        <div class="controls">
          <button class="small edit" onclick="editContact('${doc.id}')">Edit</button>
          <button class="small" onclick="deleteContact('${doc.id}')">Delete</button>
        </div>
      `;
      contactsList.appendChild(li);
    });
  }, err => {
    console.error('Snapshot error', err);
    contactsList.innerHTML = '<li style="color:#b91c1c">Error loading contacts (see console)</li>';
  });

  // Expose functions so button onclicks can call them
  window.editContact = function(id){
    db.collection('contacts').doc(id).get().then(doc => {
      if (!doc.exists) return alert('Document not found');
      const data = doc.data();
      nameInput.value = data.name || '';
      emailInput.value = data.email || '';
      phoneInput.value = data.phone || '';
      editingId = id;
      saveBtn.textContent = 'Update Contact';
      cancelEditBtn.style.display = 'inline-block';
      nameInput.focus();
    }).catch(err => console.error('Get doc error', err));
  };

  window.deleteContact = function(id){
    if (!confirm('Delete this contact?')) return;
    db.collection('contacts').doc(id).delete().catch(err => console.error('Delete error', err));
  };

  // tiny helper to avoid XSS in displayed text
  function escapeHtml(s){
    if (!s) return '';
    return s.replace(/[&<>"']/g, m => ({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;',"'":'&#39;'}[m]));
  }
  </script>
</body>
</html>
